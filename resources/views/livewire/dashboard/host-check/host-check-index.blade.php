<div class="relative min-h-96 rounded-xl">

    {{-- Charts Section --}}
    <div
        class="mb-4"
        wire:ignore
    >
        {{-- Summary Statistics --}}
        <div class="mb-4 grid grid-cols-2 gap-3 md:grid-cols-4">
            <div class="rounded-xl bg-gradient-to-r from-green-500 to-green-600 p-3 text-white shadow-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-green-100">سایت‌های فعال</p>
                        <p
                            class="text-lg font-bold"
                            id="activeSitesCount"
                        >-</p>
                    </div>
                    <div class="rounded-full bg-green-400 p-2">
                        <svg
                            class="h-4 w-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                            ></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="rounded-xl bg-gradient-to-r from-red-500 to-red-600 p-3 text-white shadow-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-red-100">سایت‌های غیرفعال</p>
                        <p
                            class="text-lg font-bold"
                            id="inactiveSitesCount"
                        >-</p>
                    </div>
                    <div class="rounded-full bg-red-400 p-2">
                        <svg
                            class="h-4 w-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                            ></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 p-3 text-white shadow-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-blue-100">میانگین uptime</p>
                        <p
                            class="text-lg font-bold"
                            id="averageUptime"
                        >-</p>
                    </div>
                    <div class="rounded-full bg-blue-400 p-2">
                        <svg
                            class="h-4 w-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                            ></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="rounded-xl bg-gradient-to-r from-purple-500 to-purple-600 p-3 text-white shadow-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-purple-100">کل بررسی‌ها</p>
                        <p
                            class="text-lg font-bold"
                            id="totalChecks"
                        >-</p>
                    </div>
                    <div class="rounded-full bg-purple-400 p-2">
                        <svg
                            class="h-4 w-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
                            ></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        {{-- Site Availability Overview Chart --}}
        <div class="mb-4 rounded-xl bg-white p-4 shadow-md">
            <div class="mb-3 flex items-center justify-between">
                <h3 class="text-base font-bold text-gray-800">وضعیت کلی در دسترس بودن سایت‌ها</h3>
                <div class="flex gap-2">
                    <div class="flex items-center gap-1">
                        <div class="h-3 w-3 rounded-full bg-green-500"></div>
                        <span class="text-xs text-gray-600">در دسترس</span>
                    </div>
                    <div class="flex items-center gap-1">
                        <div class="h-3 w-3 rounded-full bg-red-500"></div>
                        <span class="text-xs text-gray-600">غیر قابل دسترس</span>
                    </div>
                </div>
            </div>
            <canvas
                class="w-full"
                id="siteAvailabilityChart"
            ></canvas>
        </div>

        {{-- Site Detail Chart (Initially Hidden) --}}
        <div
            class="mb-4 rounded-xl bg-white p-4 shadow-md"
            id="siteDetailContainer"
            style="display: none;"
        >
            <div class="mb-3 flex items-center justify-between">
                <h3
                    class="text-base font-bold text-gray-800"
                    id="siteDetailTitle"
                >جزئیات سایت</h3>
                <button
                    class="rounded-lg bg-gray-100 px-3 py-1 text-sm text-gray-600 hover:bg-gray-200"
                    onclick="hideSiteDetail()"
                >
                    بستن
                </button>
            </div>
            <canvas
                class="w-full"
                id="siteDetailChart"
            ></canvas>
        </div>
    </div>

    <div
        class="overflow-hidden rounded-t-xl"
        x-data="{ fillter: false }"
        wire:ignore
    >
        <div class="rounded-t-xl bg-gray-800 px-3 py-1.5">
            <div class="flex items-center justify-between">
                <div>
                    <span class="block text-base text-white max-md:text-sm">تاریخچه بررسی up time بودن سایت ها</span>
                </div>
                <div
                    class="flex items-center gap-4"
                    x-data="playerState()"
                    x-init="checkState()"
                >

                    <div x-show="isPlaying">
                        <span
                            class="text-sm text-gray-500"
                            x-text="countdown"
                        ></span>
                        <span class="text-sm text-gray-500">ثانیه</span>
                    </div>
                    <div
                        class="text-sm font-bold text-gray-100"
                        x-show="isPlaying"
                    >
                        زمان بروزرسانی </div>
                    <button
                        class="flex items-center gap-2 text-white"
                        @click="togglePlay"
                    >
                        <span :class="!isPlaying ? 'text-green-500' : 'text-gray-500'">
                            <svg
                                class="size-6"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M15.75 5.25v13.5m-7.5-13.5v13.5"
                                />
                            </svg>
                        </span>
                        <span :class="isPlaying ? 'text-green-500' : 'text-gray-500'">
                            <svg
                                class="size-6"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"
                                />
                            </svg>
                        </span>
                    </button>

                    <button
                        class="flex items-center gap-2 p-2"
                        @click="fillter = !fillter"
                    >
                        <svg
                            class="h-6 w-6 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"
                            />
                        </svg>
                        <span class="text-base text-white">فیلتر</span>
                    </button>

                </div>
            </div>
        </div>
        <form
            class="tranfsition-all transform overflow-hidden bg-gray-800 px-6"
            wire:submit="fillter"
            x-cloak
            :class="fillter ? 'h-auto py-4' : 'h-0'"
        >

            <div class="my-3 grid grid-cols-1 gap-3 md:grid-cols-8">

                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="phone"
                    >شماره تماس:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="phone"
                        type="text"
                        wire:model="data.phone"
                    >
                    @error('data.phone')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>

                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="balance"
                    >موجودی بیش از :</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-center text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="balance"
                        type="text"
                        wire:model="data.balance"
                        onkeyup="javascript:this.value=Comma(this.value);"
                    >
                    @error('data.balance')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="deposit"
                    >بازدید سپرده:</label>
                    <select
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="deposit"
                        wire:model="data.deposit"
                    >
                        <option value="">--انتخاب کنید--</option>

                    </select>
                    @error('data.deposit')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="ads"
                    >تبلیغات:</label>
                    <select
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="ads"
                        wire:model="data.ads"
                    >
                        <option value="">--انتخاب کنید--</option>

                    </select>
                    @error('data.ads')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="gate"
                    >بازدید درگاه پرداخت:</label>
                    <select
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="gate"
                        wire:model="data.gate"
                    >
                        <option value="">--انتخاب کنید--</option>

                    </select>
                    @error('data.gate')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>

                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="data_start"
                    >از تاریخ:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="data_start"
                        data-jdp
                        type="text"
                        wire:model="data.data_start"
                    >
                    @error('data.data_start')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="date_end"
                    >تا تاریخ:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="date_end"
                        data-jdp
                        type="text"
                        wire:model="data.date_end"
                    >
                    @error('data.date_end')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
            </div>

            <div class="flex flex-row-reverse items-center gap-3 py-3">

                <button
                    class="rounded-lg bg-red-500 px-6 py-1 text-white transition-all hover:bg-red-600 disabled:bg-gray-100 disabled:text-gray-400"
                    type="submit"
                >
                    <svg
                        class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                        role="status"
                        aria-hidden="true"
                        wire:loading
                        wire:target="fillter"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="text-sm">اعمال فیلتر</span>
                </button>
                <button
                    class="rounded-lg bg-gray-300 px-6 py-1 text-gray-600 transition-all hover:bg-gray-200 disabled:bg-gray-100 disabled:text-gray-400"
                    type="button"
                    wire:click="ClearFillter"
                >
                    <svg
                        class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                        role="status"
                        aria-hidden="true"
                        wire:loading
                        wire:target="ClearFillter"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="text-sm">حذف فیلترها</span>
                </button>
            </div>
        </form>
    </div>

    <div class="min-h-96 rounded-b-xl bg-white">
        <div class="flex items-center justify-between">
            <div
                class="flex flex-wrap gap-1 p-2"
                x-data="{ activeFilter: 'today' }"
            >
                <div>
                    <button
                        class="flex shrink-0 items-center justify-center rounded-2xl border-2 px-4 py-2 text-sm"
                        :class="activeFilter === '30min' ? 'border-green-500 bg-green-100 text-green-700' :
                            'border-gray-100 text-gray-700 hover:bg-gray-100'"
                        wire:click="setTimeFilter('30min')"
                        @click="activeFilter = '30min'"
                    >
                        <span class="whitespace-nowrap">30 دقیقه گذشته</span>
                    </button>
                </div>
                <!-- 1 تا 3 ساعت گذشته -->
                <div>
                    <button
                        class="flex shrink-0 items-center justify-center rounded-2xl border-2 px-4 py-2 text-sm"
                        :class="activeFilter === '1h' ? 'border-green-500 bg-green-100 text-green-700' :
                            'border-gray-100 text-gray-700 hover:bg-gray-100'"
                        wire:click="setTimeFilter('1h')"
                        @click="activeFilter = '1h'"
                    >
                        <span class="whitespace-nowrap">1 ساعت گذشته</span>
                    </button>
                </div>

                <div>
                    <button
                        class="flex shrink-0 items-center justify-center rounded-2xl border-2 px-4 py-2 text-sm"
                        :class="activeFilter === '3h' ? 'border-green-500 bg-green-100 text-green-700' :
                            'border-gray-100 text-gray-700 hover:bg-gray-100'"
                        wire:click="setTimeFilter('3h')"
                        @click="activeFilter = '3h'"
                    >
                        <span class="whitespace-nowrap">3 ساعت گذشته</span>
                    </button>
                </div>

                <!-- امروز -->
                <div>
                    <button
                        class="flex shrink-0 items-center justify-center rounded-2xl border-2 px-4 py-2 text-sm"
                        :class="activeFilter === 'today' ? 'border-green-500 bg-green-100 text-green-700' :
                            'border-gray-100 text-gray-700 hover:bg-gray-100'"
                        wire:click="setTimeFilter('today')"
                        @click="activeFilter = 'today'"
                    >
                        <span class="whitespace-nowrap">امروز</span>
                    </button>
                </div>

                <!-- 3 روز گذشته -->
                <div>
                    <button
                        class="flex shrink-0 items-center justify-center rounded-2xl border-2 px-4 py-2 text-sm"
                        :class="activeFilter === '3d' ? 'border-green-500 bg-green-100 text-green-700' :
                            'border-gray-100 text-gray-700 hover:bg-gray-100'"
                        wire:click="setTimeFilter('3d')"
                        @click="activeFilter = '3d'"
                    >
                        <span class="whitespace-nowrap">3 روز گذشته</span>
                    </button>
                </div>

                <!-- هفته جاری -->
                <div>
                    <button
                        class="flex shrink-0 items-center justify-center rounded-2xl border-2 px-4 py-2 text-sm"
                        :class="activeFilter === 'this_week' ? 'border-green-500 bg-green-100 text-green-700' :
                            'border-gray-100 text-gray-700 hover:bg-gray-100'"
                        wire:click="setTimeFilter('this_week')"
                        @click="activeFilter = 'this_week'"
                    >
                        <span class="whitespace-nowrap">هفته جاری</span>
                    </button>
                </div>

                <!-- هفته گذشته -->
                <div>
                    <button
                        class="flex shrink-0 items-center justify-center rounded-2xl border-2 px-4 py-2 text-sm"
                        :class="activeFilter === 'last_week' ? 'border-green-500 bg-green-100 text-green-700' :
                            'border-gray-100 text-gray-700 hover:bg-gray-100'"
                        wire:click="setTimeFilter('last_week')"
                        @click="activeFilter = 'last_week'"
                    >
                        <span class="whitespace-nowrap">هفته گذشته</span>
                    </button>
                </div>
            </div>
            <div
                class="flex flex-wrap gap-1 p-2"
                x-data="{
                    activeStatuses: @entangle('statusFilter').defer,
                    isActive(status) {
                        return this.activeStatuses.includes(status);
                    }
                }"
            >

                <div>
                    <button
                        class="flex shrink-0 items-center justify-center rounded-2xl border-2 px-4 py-2 text-sm"
                        :class="activeStatuses.length === 0 ? 'border-green-500 bg-green-100 text-green-700' :
                            'border-gray-100 text-gray-700 hover:bg-gray-100'"
                        @click="activeStatuses = []"
                        wire:click="$set('statusFilter', [])"
                    >
                        <span class="whitespace-nowrap">همه</span>
                    </button>
                </div>

                @foreach ($availableStatuses as $status => $label)
                    <div>
                        <button
                            class="flex shrink-0 items-center justify-center rounded-2xl border-2 px-4 py-2 text-sm"
                            :class="isActive('{{ $status }}') ? 'border-green-500 bg-green-100 text-green-700' :
                                'border-gray-100 text-gray-700 hover:bg-gray-100'"
                            @click="
                    if (isActive('{{ $status }}')) {
                        activeStatuses = activeStatuses.filter(s => s !== '{{ $status }}');
                    } else {
                        activeStatuses.push('{{ $status }}');
                    }
                "
                            wire:click="setStatusFilter('{{ $status }}')"
                        >
                            <span class="whitespace-nowrap">{{ $label }}</span>
                        </button>
                    </div>
                @endforeach
            </div>
        </div>
        <div class="w-full">

            <div class="relative bg-white">
                @include('layouts.tools.loading')

                <div
                    class="w-full overflow-x-auto"
                    wire:key="{{ now()->timestamp }}"
                >
                    <table class="w-full whitespace-nowrap">
                        <thead class="bg-gray-50">
                            <tr
                                class="mb-3 h-16 rounded border border-gray-200 focus:outline-none"
                                tabindex="0"
                            >

                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">SITE</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span
                                        class="text-sm font-bold"
                                        title="IR/TEH"
                                    >IR/TEH</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span
                                        class="text-sm font-bold"
                                        title="IR/SHZ"
                                    >IR/SHZ</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span
                                        class="text-sm font-bold"
                                        title="IR/KRJ"
                                    >IR/KRJ</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span
                                        class="text-sm font-bold"
                                        title="IR/Mashhad"
                                    >IR/MHD</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span
                                        class="text-sm font-bold"
                                        title="IR/Esfahan"
                                    >IR/ISF</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span
                                        class="text-sm font-bold"
                                        title="Canada/Vancouver"
                                    >CA/YVR</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span
                                        class="text-sm font-bold"
                                        title="Germany/Nuremberg"
                                    >DE/NUE</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span
                                        class="text-sm font-bold"
                                        title="Germany/Frankfurt"
                                    >DE/FRA</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span
                                        class="text-sm font-bold"
                                        title="France/Paris"
                                    >FR/PRS</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span
                                        class="text-sm font-bold"
                                        title="Netherlands/Meppel"
                                    >NL/MPL</span>
                                </td>
                                <!-- Turkey - Istanbul -->
                                <td class="border border-gray-200 px-2 text-center">
                                    <span
                                        class="text-sm font-bold"
                                        title="Turkey/Istanbul"
                                    >TR/IST</span>
                                </td>

                                <!-- Turkey - Gebze -->
                                <td class="border border-gray-200 px-2 text-center">
                                    <span
                                        class="text-sm font-bold"
                                        title="Turkey/Gebze"
                                    >TR/GEB</span>
                                </td>

                                <!-- UK - Coventry -->
                                <td class="border border-gray-200 px-2 text-center">
                                    <span
                                        class="text-sm font-bold"
                                        title="UK/Coventry"
                                    >GB/CVT</span>
                                </td>

                                <!-- USA - Los Angeles -->
                                <td class="border border-gray-200 px-2 text-center">
                                    <span
                                        class="text-sm font-bold"
                                        title="USA/Los Angeles"
                                    >US/LAX</span>
                                </td>

                                <!-- USA - Dallas -->
                                <td class="border border-gray-200 px-2 text-center">
                                    <span
                                        class="text-sm font-bold"
                                        title="USA/Dallas"
                                    >US/DFW</span>
                                </td>

                                <!-- USA - Atlanta -->
                                <td class="border border-gray-200 px-2 text-center">
                                    <span
                                        class="text-sm font-bold"
                                        title="USA/Atlanta"
                                    >US/ATL</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span
                                        class="text-sm font-bold"
                                        title="Finland/Helsinki"
                                    >Status</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">SETTING</span>
                                </td>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($histories as $item)
                                <tr
                                    class="h-16 rounded border border-gray-200 transition-all hover:bg-gray-100 focus:outline-none"
                                    tabindex="0"
                                >
                                    <td
                                        class="px-2 py-2 text-center"
                                        style="font-family: Arial, Helvetica, sans-serif"
                                    >

                                        <div
                                            class="block border-b border-gray-400 p-1 text-left text-sm font-bold"
                                            dir="ltr"
                                        >{{ $item->host }}</div>
                                        <div
                                            class="bg-gray-50 p-1 text-left text-sm text-gray-800"
                                            dir="ltr"
                                        >{{ shamsiDate($item->created_at) }}</div>
                                    </td>
                                    @php
                                        $targetNodes = [
                                            'ir1.node.check-host.net',
                                            'ir3.node.check-host.net',
                                            'ir6.node.check-host.net',
                                            'ir2.node.check-host.net',
                                            'ir5.node.check-host.net',
                                            'ca1.node.check-host.net',
                                            'de1.node.check-host.net',
                                            'de4.node.check-host.net',
                                            'fr2.node.check-host.net',
                                            'nl2.node.check-host.net',
                                            'tr1.node.check-host.net',
                                            'tr2.node.check-host.net',
                                            'uk1.node.check-host.net',
                                            'us1.node.check-host.net',
                                            'us2.node.check-host.net',
                                            'us3.node.check-host.net',
                                        ];

                                        $result = is_array($item->result)
                                            ? $item->result
                                            : json_decode($item->result, true);

                                    @endphp

                                    @foreach ($targetNodes as $node)
                                        @php

                                            $checks = $result[$node][0] ?? null;
                                            $status = $checks[3] ?? null;
                                            $errorMessage = $checks[2] ?? null;
                                            $color = 'bg-gray-200';

                                            if ($status) {
                                                $color = $status == '200' ? 'bg-green-500' : 'bg-red-500';
                                            } elseif (isset($errorMessage)) {
                                                $color = 'bg-red-500';
                                            }
                                        @endphp

                                        <td class="justify-center px-2 text-center">

                                            <span
                                                class="{{ $color }} mx-auto block h-5 w-5 shrink-0 rounded-full p-0.5 text-xs"
                                                title="{{ $node }} - {{ $errorMessage ?? ($status ?? ($item->result['error'] ?? ($item->response['error'] ?? 'Unknown error'))) }}"
                                            >

                                            </span>

                                        </td>
                                    @endforeach
                                    <td class="justify-center px-2 text-center">
                                        <span
                                            class="{{ getStatusRequest($item->status)['class'] }} text-sm">{{ $item->status }}</span>
                                    </td>
                                    <td class="justify-center px-2 text-center">
                                        @if (isset($item->request_id))
                                            <span class="mx-auto">
                                                <a
                                                    class="mx-auto text-gray-400 transition-all hover:text-red-500"
                                                    href="https://check-host.net/check-result/{{ $item->request_id }}"
                                                    target="_blank"
                                                >
                                                    <svg
                                                        class="size-6"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        fill="none"
                                                        viewBox="0 0 24 24"
                                                        stroke-width="1.5"
                                                        stroke="currentColor"
                                                    >
                                                        <path
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            d="M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"
                                                        />
                                                    </svg>

                                                </a>
                                            </span>
                                        @else
                                        @endif
                                    </td>
                                </tr>
                                @if (isset($item->result['error']) || isset($item->response['error']))
                                    <tr class="w-full bg-red-100">
                                        <td
                                            class="justify-center px-2 text-left"
                                            colspan="20"
                                        >
                                            <p class="text-sm text-red-500">
                                                {{ $item->result['error'] ?? $item->response['error'] }}
                                            </p>
                                        </td>
                                    </tr>
                                @endif
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div
                    class="w-full bg-gray-100 p-3"
                    wire:loading.remove
                >
                    {{ $histories->links(data: ['dark' => false]) }}
                </div>
            </div>
        </div>
        <style>
            .checkbox:checked+.check-icon {
                display: flex;
            }
        </style>

    </div>
    {{-- <div class="bg-white py-6">
        <livewire:dashboard.host-check.report-host-check lazy />
    </div> --}}
</div>

@push('script')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                if (typeof Chart !== 'undefined') {
                    initHostCheckCharts();
                } else {
                    console.error('Chart.js not loaded');
                }
            }, 1000);
        });

        let siteAvailabilityChart = null;
        let siteDetailChart = null;

        function initHostCheckCharts() {
            const chartData = @json($chartData);

            if (!chartData || chartData.length === 0) {
                console.warn('No chart data available');
                return;
            }

            console.log('Initializing host check charts with data:', chartData);

            // Debug: نمایش اطلاعات تفصیلی
            chartData.forEach(item => {
                console.log(
                    `Host: ${item.host}, Uptime: ${item.uptime}%, Successful: ${item.successful_checks}/${item.total_checks}`,
                    item.debug_info);
            });

            // Group data by host
            const hostData = {};
            chartData.forEach(item => {
                if (!hostData[item.host]) {
                    hostData[item.host] = [];
                }
                hostData[item.host].push(item);
            });

            // Prepare data for site availability chart
            const siteLabels = Object.keys(hostData);
            const siteUptimes = siteLabels.map(host => {
                const hostLogs = hostData[host];
                const avgUptime = hostLogs.reduce((sum, log) => sum + log.uptime, 0) / hostLogs.length;
                return Math.round(avgUptime * 100) / 100;
            });

            // Update summary statistics
            updateSummaryStats(siteLabels, siteUptimes, chartData);

            // Site Availability Overview Chart
            const ctx1 = document.getElementById('siteAvailabilityChart').getContext('2d');
            siteAvailabilityChart = new Chart(ctx1, {
                type: 'bar',
                data: {
                    labels: siteLabels,
                    datasets: [{
                        label: 'درصد در دسترس بودن',
                        data: siteUptimes,
                        backgroundColor: siteUptimes.map(uptime =>
                            uptime >= 95 ? 'rgba(34, 197, 94, 0.8)' :
                            uptime >= 80 ? 'rgba(251, 191, 36, 0.8)' :
                            'rgba(239, 68, 68, 0.8)'
                        ),
                        borderColor: siteUptimes.map(uptime =>
                            uptime >= 95 ? 'rgba(34, 197, 94, 1)' :
                            uptime >= 80 ? 'rgba(251, 191, 36, 1)' :
                            'rgba(239, 68, 68, 1)'
                        ),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    aspectRatio: 2.5,
                    onClick: function(event, elements) {
                        if (elements.length > 0) {
                            const index = elements[0].index;
                            const selectedHost = siteLabels[index];
                            showSiteDetail(selectedHost, hostData[selectedHost]);
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const index = context.dataIndex;
                                    const hostName = siteLabels[index];
                                    const hostLogs = hostData[hostName];
                                    const avgSuccessful = Math.round(hostLogs.reduce((sum, log) => sum + log
                                        .successful_checks, 0) / hostLogs.length);
                                    const totalNodes = 16;
                                    return [
                                        `در دسترس بودن: ${context.parsed.y}%`,
                                        `میانگین Node های موفق: ${avgSuccessful}/${totalNodes}`,
                                        `Node های ناموفق: ${totalNodes - avgSuccessful}`
                                    ];
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 0
                            }
                        }
                    }
                }
            });

            console.log('Host check charts initialized successfully!');
        }

        function updateSummaryStats(siteLabels, siteUptimes, chartData) {
            // Calculate statistics
            const activeSites = siteUptimes.filter(uptime => uptime >= 80).length;
            const inactiveSites = siteUptimes.filter(uptime => uptime < 80).length;
            const averageUptime = siteUptimes.length > 0 ?
                Math.round((siteUptimes.reduce((sum, uptime) => sum + uptime, 0) / siteUptimes.length) * 100) / 100 : 0;
            const totalChecks = chartData.reduce((sum, item) => sum + item.total_checks, 0);

            // Update DOM elements
            document.getElementById('activeSitesCount').textContent = activeSites;
            document.getElementById('inactiveSitesCount').textContent = inactiveSites;
            document.getElementById('averageUptime').textContent = averageUptime + '%';
            document.getElementById('totalChecks').textContent = new Intl.NumberFormat('fa-IR').format(totalChecks);
        }

        function showSiteDetail(host, hostLogs) {
            const container = document.getElementById('siteDetailContainer');
            const title = document.getElementById('siteDetailTitle');

            title.textContent = `جزئیات سایت: ${host}`;
            container.style.display = 'block';

            // Prepare time series data
            const timeLabels = hostLogs.map(log => log.created_at);
            const uptimeData = hostLogs.map(log => log.uptime);

            // Destroy existing chart if exists
            if (siteDetailChart) {
                siteDetailChart.destroy();
            }

            // Create site detail chart
            const ctx2 = document.getElementById('siteDetailChart').getContext('2d');
            siteDetailChart = new Chart(ctx2, {
                type: 'line',
                data: {
                    labels: timeLabels,
                    datasets: [{
                        label: 'درصد در دسترس بودن',
                        data: uptimeData,
                        borderColor: 'rgba(59, 130, 246, 1)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: true,
                        tension: 0.3,
                        pointBackgroundColor: uptimeData.map(uptime =>
                            uptime >= 95 ? 'rgba(34, 197, 94, 1)' :
                            uptime >= 80 ? 'rgba(251, 191, 36, 1)' :
                            'rgba(239, 68, 68, 1)'
                        ),
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    aspectRatio: 2,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const index = context.dataIndex;
                                    const logData = hostLogs[index];
                                    return [
                                        `در دسترس بودن: ${context.parsed.y}%`,
                                        `Node های موفق: ${logData.successful_checks}/${logData.total_checks}`,
                                        `زمان: ${logData.created_at}`
                                    ];
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 0
                            }
                        }
                    }
                }
            });

            // Scroll to detail chart
            container.scrollIntoView({
                behavior: 'smooth'
            });
        }

        function hideSiteDetail() {
            const container = document.getElementById('siteDetailContainer');
            container.style.display = 'none';

            if (siteDetailChart) {
                siteDetailChart.destroy();
                siteDetailChart = null;
            }
        }

        // Listen for Livewire updates
        document.addEventListener('livewire:load', function() {
            setTimeout(initHostCheckCharts, 500);
        });

        // Reinitialize charts when filters change
        Livewire.on('filtersChanged', () => {
            setTimeout(function() {
                if (siteAvailabilityChart) {
                    siteAvailabilityChart.destroy();
                }
                if (siteDetailChart) {
                    siteDetailChart.destroy();
                    siteDetailChart = null;
                }
                hideSiteDetail();
                initHostCheckCharts();
            }, 100);
        });
    </script>
@endpush
