<?php

namespace App\Repositories;

use App\Models\Article;
use App\Models\Category;
use App\Repositories\Interfaces\ArticleRepositoryInterface;
use Illuminate\Support\Facades\DB;

class ArticleRepository implements ArticleRepositoryInterface
{
    public function getAll()
    {
        return Article::where('type', 'article')
            ->where('status', 'published')
            ->latest()
            ->simplePaginate(10);
    }

    public function getByCategorySlug($slug)
    {
        $category = Category::where('slug', $slug)->first();

        return Article::where('type', 'article')
            ->where('active', true)
            ->where('status', 'published')
            ->when($category, fn ($q) => $q->where('category_id', $category->id))
            ->latest()
            ->simplePaginate(10);
    }

    public function findByPageSlug($page)
    {

        return DB::connection('mongodb')->table('articles')
            ->where('page', '/'.$page)
            ->where('status', 'published')
            ->first();
    }

    public function findByArticleSlug($slug)
    {
        return Article::where('slug', $slug)
            ->where('active', true)
            ->where('status', 'published')
            ->latest()
            ->first();
    }

    public function incrementViewCount($id, $connection = 'mongodb')
    {
        $article = DB::connection($connection)->table('articles')->where('id', $id)->first();

        if ($article && isset($article->view_count)) {
            DB::connection($connection)->table('articles')
                ->where('id', $id)
                ->update(['view_count' => $article->view_count + 1]);
        }
    }
}
