<?php

namespace App\Http\Resources;

use App\Models\Article;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ArticleSingleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        return [
            'id' => $this->id,
            'article_parent_id' => $this->getAncestors(),
            'type' => 'article',
            'cover' => 'https://dl.khodrox.com/' . $this->galleries()?->latest()?->first()?->image ?? null,
            'title' => $this->title ?? null,
            'slug' => $this->slug ?? null,
            'author_fullname' => $this->author->fullname ?? 'مدیر سایت',
            'author_avatar' => $this->author->avatar ?? null,
            'author_about' => $this->author->about ?? 'خودراکس یک پلتفرم جامع برای دریافت خدمات آنلاین مربوط به خلافی خودرو، موتورسیکلت و سایر سرویس‌های مرتبط با وسایل نقلیه است. همچنین، این سایت دارای فروشگاه اختصاصی برای محصولات مرتبط می‌باشد.',
            'category' => [
                'title' => $this->category->title ?? null,
                'slug' => $this->category->slug ?? null,
            ],
            'date_ago' => get_ago($this->created_at),
            'comments_count' => 0,
            'meta_title' => $this->meta_title ?? null,
            'meta_description' => $this->meta_description ?? null,
            'description' => $this->description ?? null,
            'schema' => $this->schema ?? null,
            'tags' => $this->tags ?? null,
            'faqs' => $this->faqs ?? null,
            'comments' => [],
            'sidebar_right' => (bool) $this->sidebar ?? false,
            'ads' => [
                'cover' => 'https://dl.khodrox.com/' . $this->ads['cover'] ?? null,
                'title' => $this->ads['title'] ?? null,
                'description' => $this->ads['description'] ?? null,
                'link' => $this->ads['link'] ?? null,
            ],
            'article_default' => [
                'title' => 'خلافی خودرو دولتی؛ راهنمای استعلام و پرداخت',
                'slug' => 'خلافی-خودرو-دولتی',
                'cover' => 'https://dl.khodrox.com/' . $this->galleries()?->latest()?->first()?->image ?? null,
            ],
            'meta_search' => $this?->meta_search ?? null,
            'canonical' => $this?->canonical ?? null,
            'random_articles' => Article::where('_id', '!=', $this->id)
                ->where('active', true)
                ->where(function ($q) {
                    $q->whereNull('page')
                        ->orWhere('page', '');
                })
                ->where('status', 'published')
                ->limit(20)
                ->get()
                ->shuffle()
                ->take(4)
                ->map(function ($article) {
                    $cover = optional($article->galleries()->latest()->first())->image;
                    if ($article->slug != '') {
                        return [
                            'title' => $article->title,
                            'slug' => $article->slug,
                            'cover' => $cover ? 'https://dl.khodrox.com/' . $cover : null,
                        ];
                    }

                }),
        ];
    }
}
