<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InquiryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $Payements = [
            'BillID' => $this['BillID'],
            'PaymentID' => $this['PaymentID'],
        ];

        return [
            'unique_id' => $this['UniqueID'] ?? null,
            'amount' => isset($this['Amount']) ? formatMoney($this['Amount'] / 10) : null,
            'bill_id' => $this['BillID'] ?? null,
            'city' => $this['City'] ?? null,
            'date_time' => isset($this['DateTime']) ? shamsiDate($this['DateTime']) : null,
            'delivery' => $this['Delivery'] ?? null,
            'has_image' => $this['HasImage'] ?? null,
            'location' => $this['Location'] ?? null,
            'officer_identification_code' => $this['OfficerIdentificationCode'] ?? null,
            'payment_id' => $this['PaymentID'] ?? null,
            'serial_number' => $this['SerialNumber'] ?? null,
            'type' => $this['Type'] ?? null,
            'type_code' => $this['TypeCode'] ?? null,
            'payment_url' => isset($this['BillID'], $this['PaymentID'])
                ? generatePaymentLink($this['BillID'], $this['PaymentID'], $this['UniqueID'])
                : null,
        ];
    }
}
