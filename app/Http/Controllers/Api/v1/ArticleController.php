<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Http\Resources\ArticleResource;
use App\Http\Resources\ArticleSingleResource;
use App\Http\Resources\ArticlesResource;
use App\Http\Resources\CategorySingleResource;
use App\Models\Category;
use App\Repositories\Interfaces\ArticleRepositoryInterface;
use Illuminate\Http\Request;

class ArticleController extends BaseController
{
    protected $articleRepository;

    public function __construct(ArticleRepositoryInterface $articleRepository)
    {
        $this->articleRepository = $articleRepository;
    }

    public function index(Request $request)
    {
        $articles = $request->has('category')
            ? $this->articleRepository->getByCategorySlug($request->category)
            : $this->articleRepository->getAll();

        return ArticlesResource::collection($articles)
            ->additional([
                'success' => true,
                'status' => 200,
            ]);
    }

    public function show($page)
    {
        $article = $this->articleRepository->findByPageSlug($page);

        if ($article) {
            // $this->articleRepository->incrementViewCount($article->_id); // فرض MongoDB ObjectID

            return $this->sendResponse('نتیجه درخواست', new ArticleResource($article));
        }

        return $this->sendError('یافت نگردید', 'مقاله مورد نظر یافت نگردید');
    }

    public function slug($slug)
    {
        $category = Category::where('slug', $slug)->latest()->first();
        if ($category) {
            return $this->sendResponse('نتیجه درخواست', new CategorySingleResource($category));
        }

        $article = $this->articleRepository->findByArticleSlug($slug);

        if ($article) {
            // $this->articleRepository->incrementViewCount($article->id);

            return $this->sendResponse('نتیجه درخواست', new ArticleSingleResource($article));
        }

        return $this->sendError('یافت نگردید', 'مقاله مورد نظر یافت نگردید');
    }
}
