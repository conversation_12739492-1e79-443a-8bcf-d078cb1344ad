<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Http\Controllers\Controller;
use App\Http\Resources\Sitemap;
use App\Models\Article;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
class SitemapController extends BaseController
{
    public function index($type)
    {

        switch ($type) {
            case 'articles':

                $responseData = Sitemap::collection(
                    Article::where('active', true)
                        ->where(function ($q) {
                            $q->whereNull('page')
                                ->orWhere('page', '');
                        })
                        ->where('status', 'published')
                        ->get()
                );
                return $this->sendResponse('articles', $responseData);


            case 'services':
                $responseData = Sitemap::collection(
                    Article::where('active', true)
                        ->whereNotNull('page')
                        ->where('page', '!=', '')
                        ->where('status', 'published')
                        ->get()
                );
                return $this->sendResponse('services', $responseData);

            default:
                return $this->sendError('خلافی', ['message' => 'آدرس مورد نظر برای سایت مپ پیدا نشد'], Response::HTTP_NOT_FOUND);


        }
    }
}
