<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class InquiryPictureController extends BaseController
{
    public function __construct()
    {
        $this->middleware('auth:api');

    }

    public function show(Request $request)
    {

        $AMOUNT_PAYMENT_REQUEST_IMAGE = (float) str_replace(',', '', env('AMOUNT_PAYMENT_REQUEST_IMAGE'));
        $USER_BALANCE = (float) str_replace(',', '', auth()->user()->balance);
        if ($USER_BALANCE < $AMOUNT_PAYMENT_REQUEST_IMAGE) {
            return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);
        }

        $user = auth()->id();

        if (isset($user)) {
            try {
                $request->validate([
                    'inquiryId' => 'required',
                ]);

                // $inquiryId = $request->get('inquiryId');

                // $qabzinoToken = env('QABZINO_TOKEN');
                // $url = 'https://core.inquiry.ayantech.ir/webservices/core.svc/TrafficFinesInquiryGetImage';

                // $dataRequest = [
                //     'Identity' => [
                //         'Token' => $qabzinoToken,
                //     ],
                //     'Parameters' => [
                //         'UniqueID' => $inquiryId,
                //         'WalletIdentifier' => auth()->user()->phone,
                //     ],
                // ];

                // $response = Http::post($url, $dataRequest);
                // $responseData = $response->json();

                // if (
                //     empty($responseData['Parameters']) ||
                //     (isset($responseData['Status']['Code']) && $responseData['Status']['Code'] === 'GM5041')
                // ) {
                //     return $this->sendResponse('دریافت تصاویر تخلفات', $responseData, Response::HTTP_NOT_FOUND);
                // }

                // $this->withdrawMoney($AMOUNT_PAYMENT_REQUEST_IMAGE, $USER_BALANCE);

                // return $this->sendResponse('دریافت تصاویر تخلفات', $responseData);

                if ($request->get('notfound')) {
                    $responseData = [
                        'Parameters' => null,
                        'Status' => [
                            'Code' => 'GM5041',
                            'Description' => 'اطلاعات وارد شده صحیح نیست، قبض جریمه داری تصویر نمیباشد.',
                        ],
                    ];

                    return $this->sendResponse('دریافت تصاویر تخلفات', $responseData, Response::HTTP_NOT_FOUND);
                }

                $responseData = [
                    'Parameters' => [
                        'PlateImageUrl' => 'https://s3.ayanco.com/naji/trafficfineimages/6a8a608d315198664f7b9247b6d1c0471.jpg',
                        'VehicleImageUrl' => 'https://s3.ayanco.com/naji/trafficfineimages/6a8a608d315198664f7b9247b6d1c0472.jpg',
                    ],
                    'Status' => [
                        'Code' => 'G00000',
                        'Description' => 'درخواست با موفقیت انجام شد.',
                    ],
                ];

                $this->withdrawMoney($AMOUNT_PAYMENT_REQUEST_IMAGE, $USER_BALANCE);

                return $this->sendResponse('دریافت تصاویر تخلفات', $responseData);

            } catch (ValidationException $e) {
                return $this->sendError('خطای اعتبارسنجی', [
                    'message' => 'لطفاً موارد زیر را اصلاح کنید.',
                    'errors' => $e->errors(),
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        }

        return $this->sendError('خلافی', ['message' => 'شما به این بخش دسترسی ندارید لطفاْ وارد شوید'], Response::HTTP_UNAUTHORIZED);

    }

    private function withdrawMoney(float $AMOUNT_PAYMENT_REQUEST_IMAGE, float $USER_BALANCE)
    {
        if ($USER_BALANCE < $AMOUNT_PAYMENT_REQUEST_IMAGE) {
            return false;
        }

        $RESULT = $USER_BALANCE - $AMOUNT_PAYMENT_REQUEST_IMAGE;
        User::whereId(auth()->id())->update([
            'balance' => (float) $RESULT,
        ]);

        return true;
    }
}
