<?php

namespace App\Livewire\Dashboard\Articles;

use App\Models\Article;
use App\Models\Category;
use Illuminate\Support\Facades\Storage;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;

class ArticleShow extends Component
{
    use LivewireAlert, WithFileUploads;

    public $articleId;

    public $data = [
        'title' => null,
        'slug' => null,
        'category_id' => null,
        'description' => null,
        'author' => null,
        'meta_title' => null,
        'meta_description' => null,
        'meta_tags' => null,
        'view_count' => 0,
        'page' => null,
        'status' => null,
        'tags' => [],
        'chain' => null,
        'schema' => null,
        'type' => null,
        'datePublished' => null,
        'meta_search' => null,
        'cover' => null,
        'canonical' => null,
        'meta_search_toggle' => false,

        'sidebar' => false,
        'ads_toggle' => false,
        'ads_title' => null,
        'imageAds' => null,
        'ads_link' => null,

        'article_id' => null,
        'archive' => null,
        'study_time' => null
    ];

    public $question = '';

    public $answer = '';

    public $faqs = [];

    public $value;

    public $quillId;

    public $image;

    public $imageAds;

    public function mount()
    {

        // $this->value = $value;
        $article = Article::whereId($this->articleId)->firstOrFail();

        // dd($article->toArray());

        $this->data['meta_search_toggle'] = (bool) $article?->meta_search;

        $this->data['cover'] = $article?->galleries()?->latest()?->first()?->image ?? null;
        $this->image = $article?->galleries()?->latest()?->first()?->image ?? null;

        $this->data['title'] = $article?->title;
        $this->data['slug'] = $article?->slug;
        $this->data['category_id'] = $article?->category_id;
        $this->data['description'] = $article?->description;
        $this->value = $article?->description;
        $this->data['canonical'] = $article?->canonical;
        $this->data['meta_title'] = $article?->meta_title;
        $this->data['meta_description'] = $article?->meta_description;
        $this->data['status'] = $article?->status;
        $this->data['page'] = $article?->page;
        $this->data['type'] = $article?->type;
        $this->data['datePublished'] = $article?->datePublished;
        $this->data['schema'] = $article?->schema;
        $this->data['tags'] = $article?->tags ?? [];
        $this->faqs = $article?->faqs ?? [];
        $this->data['sidebar'] = $article?->sidebar ?? false;

        $this->data['article_id'] = $article?->article_id ?? null;
        $this->data['archive'] = $article?->archive ?? null;
        $this->data['study_time'] = $article?->study_time ?? null;


        $this->data['ads_toggle'] = $article?->ads_toggle ?? false;
        if ($article->ads) {
            $this->data['imageAds'] = $article['ads']['cover'] ?? null;
            $this->data['ads_title'] = $article['ads']['title'] ?? null;
            $this->data['ads_link'] = $article['ads']['link'] ?? null;
        }

    }

    #[On('reload-faqs')]
    public function reloadFaqs()
    {
        $article = Article::whereId($this->articleId)->firstOrFail();
        $this->faqs = $article?->faqs ?? [];
    }

    public function updatedDataTitle()
    {
        // if ($this->data['slug'] == null) {
        $this->data['slug'] = makeSlug($this->data['title']);
        // }
    }

    public function generateSlug()
    {
        $this->data['slug'] = makeSlug($this->data['title']);
    }

    public function removeCoverImage()
    {
        $article = Article::findOrFail($this->articleId);

        foreach ($article->galleries as $item) {
            Storage::disk('public')->delete($item->image);
            $item->delete();
        }

        $this->image = null;
        $this->data['cover'] = null;
    }

    public function addTag()
    {
        if ($this->data['chain']) {
            $tags = array_filter(array_map('trim', explode(',', $this->data['chain'])));
            $this->data['tags'] = array_unique(array_merge($this->data['tags'], $tags));
            $this->data['chain'] = null; // clear input
        }
    }

    public function removeTag($index)
    {
        unset($this->data['tags'][$index]);
        $this->data['tags'] = array_values($this->data['tags']); // reindex
    }

    public function addItem()
    {
        $this->validate([
            'question' => 'required|string',
            'answer' => 'required|string',
        ]);

        $this->faqs[] = [
            'question' => $this->question,
            'answer' => $this->answer,
        ];

        $this->reset(['question', 'answer']);
    }

    public function removeItem($index)
    {
        unset($this->faqs[$index]);
        $this->faqs = array_values($this->faqs);
    }

    public function rules()
    {
        return [
            'data.title' => 'required',
            'data.category_id' => 'required',
            'data.meta_title' => 'required',
            'data.meta_description' => 'required',
            // 'data.page' => 'required',
            'data.status' => 'required',
            'value' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'data.title.required' => 'عنوان مقاله را بنویسید',
            'data.category_id.required' => 'گروه را مشخص کنید',
            'data.meta_title.required' => 'متا تایتل را بنویسید',
            'data.meta_description.required' => 'متا دیسکریبشن را بنویسید',
            // 'data.page.required' => 'صفحه مقاله را مشخص کنید',
            'value.required' => 'مقاله مورد نظر را بنویسید',
            'data.status.required' => 'وضعیت مقاله الزامیست',
        ];
    }

    public function generateCanonical()
    {
        $this->data['canonical'] = 'https://khodrox.com/blog/' . $this->data['slug'];
    }

    public function update()
    {

        $meta_search_toggle = $this->data['meta_search_toggle'] ? 'noindex, nofollow' : null;

        if (!auth()->user()->can('show-show-update-article')) {
            $this->alert('error', 'خطا در بروزرسانی', [
                'position' => 'center',
                'timer' => 5000,
                'toast' => false,
                'text' => 'شما دسترسی لازم برای بروزرسانی را ندارید',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

            return;
        }

        // dd($this->data);

        try {
            $this->validate();

        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->alert('error', 'خطا در اعتبارسنجی', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'لطفاً تمام فیلدهای مورد نیاز را تکمیل کنید و مجدداً تلاش نمایید.',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
            throw $e;
        }

        if (empty($this->value)) {

            $this->alert('error', 'خطا در اعتبارسنجی', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'مقاله خود را بنویسید و سپس مجددا درخواست خود را ثبت کنید',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

            return;
        }


        // $copyArticle = Article::where('title', $this->data['title'])->where('archive', true)->first();
        // if ($copyArticle) {

        //     $this->alert('error', 'خطا در اعتبارسنجی', [
        //         'position' => 'center',
        //         'timer' => 3000,
        //         'toast' => false,
        //         'text' => 'عنوان مقاله تکراری می باشد لطفا بررسی کنید',
        //         'timerProgressBar' => true,
        //         'showDenyButton' => true,
        //         'denyButtonText' => 'بسیار خب متوجه شدم',
        //     ]);

        //     return;
        // }

        $this->data['description'] = $this->value;

        if ($this->imageAds) {
            $this->data['imageAds'] = saveFileFromTemporaryImage($this->imageAds);
        }


        $articleOld = Article::whereId($this->articleId)->first();

        $article = Article::create([
            'user_id' => $articleOld->user_id,
            'type' => $this->data['page'] == null ? 'article' : 'services',
            'article_id' => $this->articleId,
            'title' => $this->data['title'],
            'slug' => $this->data['slug'],
            'category_id' => $this->data['category_id'],
            'description' => $this->data['description'],
            'meta_title' => $this->data['meta_title'],
            'meta_description' => $this->data['meta_description'],
            'page' => $this->data['page'],
            'tags' => $this->data['tags'],
            'view_count' => $this->data['view_count'],
            'status' => $this->data['status'],
            'schema' => $this->data['schema'],
            'faqs' => $this->faqs,
            'datePublished' => $this->data['datePublished'],
            'meta_search' => $meta_search_toggle,
            'study_time' => $this->data['study_time'],

            'canonical' => $this->data['canonical'],

            'active' => true,

            'sidebar' => $this->data['sidebar'] ?? false,
            'ads_toggle' => $this->data['ads_toggle'] ?? false,
            'ads' => [
                'cover' => $this->data['imageAds'],
                'title' => $this->data['ads_title'],
                'description' => null,
                'link' => $this->data['ads_link'],
            ],

        ]);

        Article::whereId($this->articleId)->update([
            'active' => false,
            'status' => 'draft',
            'article_id' => $article->id,
            'archive' => true,
        ]);

        if ($this->image) {

            $path = $this->image instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                ? saveFileFromTemporaryImage($this->image)
                : $this->image;
            // $this->image->store('articles', 'public');
            // dd($path);
            $article = Article::find($article->id);
            $article->galleries()->create([
                'image' => $path,
            ]);

        }

        $this->alert('success', 'بروزرسانی موفق', [
            'position' => 'center',
            'timer' => 5000,
            'toast' => false,
            'text' => 'اطلاعات مقاله با موفقیت بروز شد',
            'timerProgressBar' => true,
            'showDenyButton' => true,
            'denyButtonText' => 'بسیار خب متوجه شدم',
        ]);

        return redirect()->route('article-show', $article->id);
        // return redirect()->route('article-manager');

        // dd($this->data);
    }

    // public function updatedImage()
    // {
    //     $path = saveFileFromTemporaryImage($this->image);
    //     $article = Article::find($this->data['article_id']);
    //     $article->galleries()->create([
    //         'image' => $path,
    //     ]);

    //     $this->data['cover'] = $this->image->temporaryUrl();
    // }

    public function updatedImage()
    {
        $path = saveFileFromTemporaryImage($this->image);
        $article = Article::find($this->articleId);

        if (!$article) {
            // Handle the error gracefully, e.g. log it or alert the user
            logger()->error('Article not found in updatedImage', ['article_id' => $this->articleId]);
            $this->alert('error', 'خطا', [
                'position' => 'center',
                'timer' => 50000,
                'toast' => false,
                'text' => 'مقاله مورد نظر یافت نشد.',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'denyButtonText' => 'متوجه شدم',
            ]);

            return;
        }

        $article->galleries()->create([
            'image' => $path,
        ]);

        $this->data['cover'] = $this->image->temporaryUrl();
    }

    public function render()
    {
        return view('livewire.dashboard.articles.article-show', [
            'categories' => Category::where('source', 'khodrox')->get(),
        ]);
    }
}
