<?php

namespace App\Livewire\Dashboard\HostCheck;

use App\Models\HostCheckLog;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithPagination;

class HostCheckIndex extends Component
{
    use WithPagination;

    #[Url(as: 'sort', keep: true)]
    public $timeFilter = 'today';

    public function setTimeFilter($filter)
    {
        $this->timeFilter = $filter;
        $this->dispatch('filtersChanged');
    }

    #[Url(as: 'status_filter', keep: true)]
    public $statusFilter = [];

    public $availableStatuses = [
        'completed' => 'موفق‌ها',
        'error' => 'شکست خورده',
        'pending' => 'در حال انتظار',
        'pending_secondary' => 'در حال پردازش',
    ];

    public function mount()
    {
        $this->statusFilter = [
            'completed',
            'pending',
            'pending_secondary',
        ];
    }

    public function setStatusFilter($status)
    {
        if (in_array($status, $this->statusFilter)) {
            $this->statusFilter = array_diff($this->statusFilter, [$status]);
        } else {
            $this->statusFilter[] = $status;
        }
        $this->dispatch('filtersChanged');
    }

    public function placeholder()
    {
        return <<<'HTML'
        <div
                    class="flex gap-3 rounded-md bg-white p-5 h-96 min-h-96 "

                >
                    <svg
                        class="inline h-6 w-6 animate-spin text-red-700"
                        role="status"
                        aria-hidden="true"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="whitespace-nowrap text-base font-bold"> درحال دریافت اطلاعات از سرور ...</span>
                </div>

        HTML;
    }

    public function getChartData()
    {
        $query = HostCheckLog::query();

        // Apply time filter for chart data
        switch ($this->timeFilter) {
            case '30min':
                $query->where('created_at', '>=', now()->subMinutes(30));
                break;
            case '1h':
                $query->where('created_at', '>=', now()->subHour());
                break;
            case '3h':
                $query->where('created_at', '>=', now()->subHours(3));
                break;
            case 'today':
                $query->whereDate('created_at', today());
                break;
            case '3d':
                $query->where('created_at', '>=', now()->subDays(3));
                break;
            case 'this_week':
                $query->whereBetween('created_at', [
                    now()->startOfWeek(),
                    now()->endOfWeek(),
                ]);
                break;
            case 'last_week':
                $query->whereBetween('created_at', [
                    now()->subWeek()->startOfWeek(),
                    now()->subWeek()->endOfWeek(),
                ]);
                break;
            default:
                $query->whereDate('created_at', today());
                break;
        }

        $logs = $query->where('status', 'completed')->get();
        $chartData = [];

        foreach ($logs as $log) {
            $result = is_array($log->result) ? $log->result : json_decode($log->result, true);

            if (!is_array($result))
                continue;

            // تعداد کل node ها (16 تا)
            $totalNodes = 16;
            $successfulNodes = 0;

            // لیست node های مورد انتظار
            $expectedNodes = [
                'ir1.node.check-host.net',
                'ir3.node.check-host.net',
                'ir6.node.check-host.net',
                'ir2.node.check-host.net',
                'ir5.node.check-host.net',
                'ca1.node.check-host.net',
                'de1.node.check-host.net',
                'de4.node.check-host.net',
                'fr2.node.check-host.net',
                'nl2.node.check-host.net',
                'tr1.node.check-host.net',
                'tr2.node.check-host.net',
                'uk1.node.check-host.net',
                'us1.node.check-host.net',
                'us2.node.check-host.net',
                'us3.node.check-host.net',
            ];

            foreach ($expectedNodes as $nodeName) {
                if (isset($result[$nodeName]) && isset($result[$nodeName][0])) {
                    $nodeResult = $result[$nodeName][0];
                    $status = $nodeResult[3] ?? null;

                    // اگر status برابر 200 باشد، node موفق است
                    if ($status == '200') {
                        $successfulNodes++;
                    }
                }
            }

            // محاسبه درصد uptime بر اساس تعداد node های موفق از کل 16 node
            $uptimePercent = round(($successfulNodes / $totalNodes) * 100, 2);

            $chartData[] = [
                'host' => $log->host,
                'uptime' => $uptimePercent,
                'total_checks' => $totalNodes,
                'successful_checks' => $successfulNodes,
                'failed_checks' => $totalNodes - $successfulNodes,
                'created_at' => $log->created_at->format('Y-m-d H:i'),
                'debug_info' => [
                    'total_nodes' => $totalNodes,
                    'successful_nodes' => $successfulNodes,
                    'result_keys' => array_keys($result),
                ]
            ];
        }

        return $chartData;
    }

    public function render()
    {
        $query = HostCheckLog::query();

        if (!empty($this->statusFilter)) {
            $query->whereIn('status', $this->statusFilter);
        }

        switch ($this->timeFilter) {
            case '30min':
                $query->where('created_at', '>=', now()->subMinutes(30));
                break;

            case '1h':
                $query->where('created_at', '>=', now()->subHour());
                break;

            case '3h':
                $query->where('created_at', '>=', now()->subHours(3));
                break;

            case 'today':
                $query->whereDate('created_at', today());
                break;

            case '3d':
                $query->where('created_at', '>=', now()->subDays(3));
                break;

            case 'this_week':
                $query->whereBetween('created_at', [
                    now()->startOfWeek(),
                    now()->endOfWeek(),
                ]);
                break;

            case 'last_week':
                $query->whereBetween('created_at', [
                    now()->subWeek()->startOfWeek(),
                    now()->subWeek()->endOfWeek(),
                ]);
                break;

            default:
                break;
        }

        $chartData = $this->getChartData();

        return view('livewire.dashboard.host-check.host-check-index', [
            'histories' => $query->latest()->paginate(perPage: 30),
            'chartData' => $chartData,
        ]);
    }
}
