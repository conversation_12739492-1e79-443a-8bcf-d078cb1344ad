<?php

use App\Models\Gate;
use App\Models\Setting;
use App\Models\User;
use Hek<PERSON>inasser\Verta\Verta;
use hisorange\BrowserDetect\Parser as Browser;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Melipayamak\MelipayamakApi;

if (! function_exists('faTOen')) {
    function faTOen($string)
    {
        return strtr($string, ['۰' => '0', '۱' => '1', '۲' => '2', '۳' => '3', '۴' => '4', '۵' => '5', '۶' => '6', '۷' => '7', '۸' => '8', '۹' => '9', '٠' => '0', '١' => '1', '٢' => '2', '٣' => '3', '٤' => '4', '٥' => '5', '٦' => '6', '٧' => '7', '٨' => '8', '٩' => '9']);
    }
}
if (! function_exists('getDateTiem')) {
    function getDateTiem($time)
    {
        return new Verta($time);
    }
}

if (! function_exists('get_time')) {
    function get_time($time)
    {
        // return Verta::persianNumbers($time);
        return $time;
    }
}

if (! function_exists('get_ago')) {
    function get_ago($time)
    {
        return verta($time)->formatDifference();
    }
}

if (! function_exists('dateTimeToday')) {
    function dateTimeToday()
    {
        $dateToday = todays();

        return $dateToday.' '.date('H:i', time());
    }
}

if (! function_exists('todays')) {
    function todays($day = null)
    {

        if (isset($day) && $day != '') {
            $v = verta('+1 day');
            $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
            $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;

            return $v->year.'/'.$month.'/'.$day;
        }

        $v = verta();
        $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
        $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;

        return $v->year.'/'.$month.'/'.$day;
    }
}

if (! function_exists('toYear')) {
    function toYear()
    {
        $v = verta();
        $v->addYear();
        $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
        $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;

        return $v->year.'/'.$month.'/'.$day;
    }
}

if (! function_exists('shamsiDate')) {
    function shamsiDate($date, $timezone = 'Asia/Tehran')
    {
        $v = new Verta($date);

        // تنظیم منطقه زمانی
        $v->timezone($timezone);

        return $v->formatJalaliDatetime();
    }
}

if (! function_exists('shamsiDateLimit')) {
    function shamsiDateLimit($date, $year = null, $timezone = 'Asia/Tehran')
    {
        $v = new Verta($date);
        $v->timezone($timezone);
        if ($year != null) {
            $v->addYear();
        }
        $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
        $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;

        return $v->year.'/'.$month.'/'.$day;
    }
}

if (! function_exists('TimeDateLimit')) {
    function TimeDateLimit($date, $timezone = 'Asia/Tehran')
    {
        $v = new Verta($date);
        $v->timezone($timezone);

        return str_pad($v->hour, 2, '0', STR_PAD_LEFT).':'.
               str_pad($v->minute, 2, '0', STR_PAD_LEFT).':'.
               str_pad($v->second, 2, '0', STR_PAD_LEFT);
    }
}

if (! function_exists('shamsiDateDetails')) {
    function shamsiDateDetails()
    {
        if (isset($day) && $day != '') {
            $v = verta('+1 day');
            $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
            $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;

            return $v->year.'/'.$month.'/'.$day;
        }

        $v = verta();
        $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
        $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;

        return $day.' '.convertIdtoTextMonth($month).' '.$v->year;
    }
}

if (! function_exists('shamsiDateArray')) {
    function shamsiDateArray()
    {
        if (isset($day) && $day != '') {
            $v = verta('+1 day');
            $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
            $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;

            return $v->year.'/'.$month.'/'.$day;
        }

        $v = verta();
        $month = strlen($v->month) == 1 ? '0'.$v->month : $v->month;
        $day = strlen($v->day) == 1 ? '0'.$v->day : $v->day;

        return ['day' => $day, 'month' => convertIdtoTextMonth($month), 'year' => $v->year];
    }
}

if (! function_exists('CarbonDate')) {
    function CarbonDate($date)
    {
        return Verta::parse($date)->datetime();
        // return $v->jalaliToGregorian();
    }
}

if (! function_exists('setUserLog')) {
    function setUserLog($request, $phone = null, $description = null)
    {
        \App\Models\UserLog::create([
            'page' => url()->current(),
            'previous_page' => url()->previous(),
            'ip' => $request->ip(),
            'phone' => $phone,
            'browser' => Browser::browserName(),
            'platform' => Browser::platformName(),
            'device' => Browser::deviceType(),
            'description' => $description,
        ]);
    }
}

if (! function_exists('fixPhoneNumber')) {
    function fixPhoneNumber($phoneNumber)
    {
        $phoneNumber = str_replace('-', '', $phoneNumber);
        $phoneNumber = faTOen($phoneNumber);
        $phoneNumber = Str::replace(' ', '', $phoneNumber);
        $phoneNumber = Str::replace('+98', '0', $phoneNumber);

        return $phoneNumber;
    }
}

if (! function_exists('maskPhoneNumber')) {
    function maskPhoneNumber($phoneNumber)
    {
        // بررسی طول شماره موبایل
        if (strlen($phoneNumber) == 11) {
            // گرفتن پیش‌شماره و اعداد انتهایی شماره
            $prefix = substr($phoneNumber, 0, 4);
            $suffix = substr($phoneNumber, -3);

            // جایگزینی اعداد وسط با ستاره
            $maskedMiddle = '****';

            // ادغام پیش‌شماره، اعداد وسط و اعداد انتهایی با یکدیگر
            $maskedNumber = $prefix.$maskedMiddle.$suffix;

            return $maskedNumber;
        } else {
            // اگر طول شماره موبایل صحیح نباشد، پیام خطا را برگردان
            return '-';
        }
    }
}

if (! function_exists('formatMoney')) {
    function formatMoney($money)
    {
        return $money != null ? number_format(intval(str_replace([',', '،'], '', $money)), 0, '.', ',') : 0;
    }
}

if (! function_exists('hashId')) {
    function hashId($id)
    {
        if (intval($id) == 0) {
            return 0;
        }
        $id = (((intval($id) * 53) + 147) - 9) + 12365;

        return $id;
    }
}

if (! function_exists('unHashId')) {
    function unHashId($id)
    {
        if (intval($id) == 0) {
            return 0;
        }
        $id = ((((intval($id) - 12365) + 9) - 147) / 53);

        return $id;
    }
}

if (! function_exists('saveFileFromTemporaryUrl')) {
    function saveFileFromTemporaryUrl($temporaryUrl, $directory = 'articles')
    {
        // try {
        // دریافت محتویات فایل
        $fileContents = file_get_contents($temporaryUrl);

        if (! $fileContents) {
            throw new Exception("Failed to retrieve file contents from URL: $temporaryUrl");
        }

        // تولید نام منحصربه‌فرد برای فایل
        $fileName = uniqid().'.'.pathinfo(parse_url($temporaryUrl, PHP_URL_PATH), PATHINFO_EXTENSION);

        // مسیر ذخیره فایل
        $storagePath = storage_path("app/public/{$directory}/");

        // اطمینان از وجود پوشه
        if (! is_dir($storagePath)) {
            mkdir($storagePath, 0755, true);
        }

        // ذخیره فایل
        file_put_contents($storagePath.$fileName, $fileContents);

        // بازگرداندن آدرس فایل ذخیره‌شده
        return "storage/{$directory}/{$fileName}";
        // } catch (Exception $e) {
        //     // مدیریت خطا
        //     return null;
        // }
    }
}

if (! function_exists('saveFileFromTemporaryImage')) {
    function saveFileFromTemporaryImage($temporaryFile, $directory = 'articles')
    {
        try {
            if (! $temporaryFile instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile) {
                throw new Exception('Invalid file input.');
            }

            // ذخیره فایل در مسیر public
            $path = $temporaryFile->store("{$directory}");

            $path = 'storage/'.$path;

            // تبدیل مسیر به آدرس قابل دسترسی از طریق مرورگر
            return str_replace('public/', 'storage/', $path);
        } catch (Exception $e) {
            return null;
        }
    }
}

if (! function_exists('PhoneNumberFix')) {
    function PhoneNumberFix(string $phone)
    {
        // حذف فاصله‌ها و سایر کاراکترهای غیر ضروری
        $phone = preg_replace('/\D/', '', $phone); // حذف همه کاراکترهای غیر عددی

        // اگر شماره با 0 شروع نمی‌شود، به ابتدای آن 0 اضافه کن
        if (! str_starts_with($phone, '0')) {
            $phone = '0'.$phone;
        }

        // اصلاح پیشوندهایی مانند +98, 0098 و 98 به 09
        $phone = preg_replace('/^(?:\+98|0098)(\d{10})$/', '09$1', $phone);

        // اگر شماره از ابتدا با 98 شروع می‌شود، فقط شماره بدون پیشوند
        if (substr($phone, 0, 2) == '98' && strlen($phone) == 12) {
            $phone = '0'.substr($phone, 2);
        }

        return $phone;
    }
}

if (! function_exists('finglishToPersian')) {
    function finglishToPersian($text)
    {
        // نگاشت دقیق‌تر برای کلمات و ترکیبات
        $mapping = [
            'kh' => 'خ',
            'sh' => 'ش',
            'ch' => 'چ',
            'zh' => 'ژ',
            'gh' => 'غ',
            'ph' => 'ف',
            'oo' => 'و',
            'aa' => 'ا',
            // حروف تکی
            'a' => 'ا',
            'b' => 'ب',
            'c' => 'ک',
            'd' => 'د',
            'e' => 'ه',
            'f' => 'ف',
            'g' => 'گ',
            'h' => 'ه',
            'i' => 'ی',
            'j' => 'ج',
            'k' => 'ک',
            'l' => 'ل',
            'm' => 'م',
            'n' => 'ن',
            'o' => 'و',
            'p' => 'پ',
            'q' => 'ق',
            'r' => 'ر',
            's' => 'س',
            't' => 'ت',
            'u' => 'و',
            'v' => 'و',
            'w' => 'و',
            'x' => 'کس',
            'y' => 'ی',
            'z' => 'ز',
        ];

        // مرتب‌سازی کلیدها براساس طول آنها (برای اولویت‌دهی ترکیبات بلندتر)
        uksort($mapping, function ($a, $b) {
            return strlen($b) - strlen($a);
        });

        // جایگزینی کاراکترهای فینگلیش با فارسی
        foreach ($mapping as $finglish => $persian) {
            $text = str_replace($finglish, $persian, $text);
        }

        return $text;
    }
}

if (! function_exists('getService')) {
    function getService(?string $service = 'default'): array
    {
        if ($service === null) {
            return [

                'text' => 'نامشخص',
                'class' => 'bg-gray-100 text-gray-700 rounded-xl px-3 py-0.5',

            ];
        }

        return match ($service) {
            'khodroyar' => [
                'text' => 'خودرویار',
                'class' => 'bg-yellow-300 text-yellow-700 rounded-xl px-3 py-0.5',
            ],
            'nikzi' => [
                'text' => 'نیکزی',
                'class' => 'bg-blue-500 text-white rounded-xl px-3 py-0.5',
            ],
            'migrofin' => [
                'text' => 'مایگروفین',
                'class' => 'bg-green-500 text-white rounded-xl px-3 py-0.5',
            ],
            'bankyar' => [
                'text' => 'بانکیار',
                'class' => 'bg-red-500 text-white rounded-xl px-3 py-0.5',
            ],
            'pishkhan724' => [
                'text' => 'پیشخوان 724',
                'class' => 'bg-purple-500 text-white rounded-xl px-3 py-0.5',
            ],
            'kouroshcc' => [
                'text' => 'کوروشسس',
                'class' => 'bg-pink-500 text-white rounded-xl px-3 py-0.5',
            ],
            'shebacart' => [
                'text' => 'شبا کارت',
                'class' => 'bg-indigo-500 text-white rounded-xl px-3 py-0.5',
            ],
            'cardtosheba' => [
                'text' => 'کارت تو شبا',
                'class' => 'bg-teal-500 text-white rounded-xl px-3 py-0.5',
            ],
            'irancheque' => [
                'text' => 'ایران چک',
                'class' => 'bg-orange-500 text-white rounded-xl px-3 py-0.5',
            ],
            'tartankala' => [
                'text' => 'تارتان کالا',
                'class' => 'bg-gray-500 text-white rounded-xl px-3 py-0.5',
            ],
            'khalafiyar' => [
                'text' => 'خلافی یار',
                'class' => 'bg-yellow-500 text-white rounded-xl px-3 py-0.5',
            ],
            'cardbecard' => [
                'text' => 'کارت به کارت',
                'class' => 'bg-blue-500 text-white rounded-xl px-3 py-0.5',
            ],
            'pishkhaneman' => [
                'text' => 'پیشخوان من',
                'class' => 'bg-green-500 text-white rounded-xl px-3 py-0.5',
            ],
            'test' => [
                'text' => 'سیستم تست',
                'class' => 'bg-red-500 text-white rounded-xl px-3 py-0.5',
            ],
            'naghlie' => [
                'text' => 'نقلیه',
                'class' => 'bg-blue-500 text-white rounded-xl px-3 py-0.5',
            ],
            'khalafionline' => [
                'text' => 'خلافی آنلاین',
                'class' => 'bg-red-500 text-white rounded-xl px-3 py-0.5',
            ],
            'khodrox' => [
                'text' => 'خودراکس',
                'class' => 'bg-green-500 text-white rounded-xl px-3 py-0.5',
            ],
            default => [
                'text' => 'نامشخص',
                'class' => 'bg-gray-100 text-gray-700 rounded-xl px-3 py-0.5',
            ],
        };
    }
}

if (! function_exists('getStatusRequest')) {
    function getStatusRequest(?string $status = 'default'): array
    {
        if ($status === null) {
            return [

                'text' => 'نامشخص',
                'class' => 'bg-gray-100 text-gray-700 rounded-xl px-3 py-0.5',

            ];
        }

        return match ($status) {
            'success' => [
                'text' => 'تایید شده',
                'class' => 'bg-green-500 text-white rounded-xl px-3 py-0.5 text-sm',
            ],
            'accept' => [
                'text' => 'تایید شده',
                'class' => 'bg-green-500 text-white rounded-xl px-3 py-0.5 text-sm',
            ],
            'completed' => [
                'text' => 'تایید شده',
                'class' => 'bg-green-500 text-white rounded-xl px-3 py-0.5 text-sm',
            ],
            'reject' => [
                'text' => 'رد شده یا تایید نشده',
                'class' => 'bg-red-500 text-white rounded-xl px-3 py-0.5 text-sm',
            ],
            'rejected' => [
                'text' => 'رد شده یا تایید نشده',
                'class' => 'bg-red-500 text-white rounded-xl px-3 py-0.5 text-sm',
            ],
            'cancel' => [
                'text' => 'کنسل شده',
                'class' => 'bg-red-500 text-white rounded-xl px-3 py-0.5 text-sm',
            ],
            'error' => [
                'text' => 'کنسل شده',
                'class' => 'bg-red-500 text-white rounded-xl px-3 py-0.5 text-sm',
            ],
            'pending' => [
                'text' => 'درحال انجام',
                'class' => 'bg-yellow-300 text-yellow-700 rounded-xl px-3 py-0.5 text-sm',
            ],
            'pending_secondary' => [
                'text' => 'درحال انجام',
                'class' => 'bg-yellow-300 text-yellow-700 rounded-xl px-3 py-0.5 text-sm',
            ],
            default => [
                'text' => '',
                'class' => '',
            ],
        };
    }
}

if (! function_exists('ShebacartFindService')) {
    function ShebacartFindService(string $service): string
    {
        return match ($service) {
            'bankyar' => 'my.bankyar.net',
            'pishkhan724' => 'my.pishkhan724.com',
            'kouroshcc' => 'my.kouroshcc.com',
            'shebacart' => 'shebacart.com',
            'irancheque' => 'my.irancheque.com',
            default => '',
        };
    }
}

if (! function_exists('ShebacartFindServiceName')) {
    function ShebacartFindServiceName(string $domain): string
    {
        return match ($domain) {
            'my.bankyar.net' => 'bankyar',
            'my.pishkhan724.com' => 'pishkhan724',
            'my.kouroshcc.com' => 'kouroshcc',
            'shebacart.com' => 'shebacart',
            'my.irancheque.com' => 'irancheque',
            default => $domain,
        };
    }
}

if (! function_exists('generatePaymentLink')) {
    function generatePaymentLink($BillID, $PaymentID, $UniqueID)
    {
        $response = Http::post(env('QABZINO_PAYMENT_BILLS_URL'), [
            'Identity' => [
                'Token' => env('QABZINO_TOKEN'),
            ],
            'Parameters' => [
                'Bills' => [
                    [
                        'BillID' => $BillID,
                        'PaymentID' => $PaymentID,
                    ],
                ],
                'MobileNumber' => '***********',
                'RedirectLink' => '',
                'RedirectLinkTitle' => 'پرداخت خلافی',
                'TerminalID' => 11,
                'TraceNumber' => $UniqueID,
                'UniqueKey' => $UniqueID,
            ],
        ]);

        $result = $response->json();

        return isset($result['Parameters']['PaymentLink']) ? $result['Parameters']['PaymentLink'] : '#';
    }
}

if (! function_exists('getUserBalance')) {
    function getUserBalance($phone, $source, $redirectTo = false)
    {
        $user = User::where('source', $source)->where('phone', $phone)->first();
        if (isset($user) && $user != null) {

            $USER_AFTER_BALANCE = (float) str_replace(',', '', $user->balance);
            $userKhalafiyarTransfer = getTransferAppSetting();
            if ($userKhalafiyarTransfer) {
                if ($source != 'khodroyar') {
                    $USER_AFTER_BALANCE = $USER_AFTER_BALANCE + getSourceBalance($phone, 'khodroyar') ?? 0;
                }
            }

            if ($redirectTo) {
                $USER_AFTER_BALANCE = getSourceBalance($phone, 'khodroyar');
            }

            return formatMoney($USER_AFTER_BALANCE);
        }

        return 0;
    }
}

if (! function_exists('getUserShebacartBalance')) {
    function getUserShebacartBalance($phone, $source)
    {

        $user = DB::connection('mongodb_shebacart')
            ->table('users')->where('source', $source)->where('phone', $phone)->first();

        if (isset($user) && $user != null) {
            return formatMoney($user?->balance);
        }

        return 0;
    }
}

if (! function_exists('getInquiryCount')) {
    function getInquiryCount($phone, $source, $data)
    {
        $cacheKey = "inquiry_counts_{$phone}_{$source}_{$data['plaque_left']}";

        // return Cache::remember($cacheKey, now()->addMinutes(15), function () use ($phone, $source, $data) {
        return DB::connection('mongodb')
            ->table('inquiry_results')
            ->where('source', $source)
            ->where('phone', $phone)
            ->where('plaque_left', $data['plaque_left'])
            // ->where('plaque_mid', $data['plaque_mid'])
            // ->where('plaque_right', $data['plaque_right'])
            // ->where('plaque_alphabet', $data['plaque_alphabet'])
            ->count();
        // });
    }
}

if (! function_exists('getMeliPayamakCredit')) {
    function getMeliPayamakCredit()
    {
        $username = env('MELI_PAYAMAK_USERNAME');
        $password = env('MELI_PAYAMAK_PASSWORD');
        $cacheKey = 'melipayamak_credit';

        $credit = Cache::remember($cacheKey, now()->addMinutes(15), function () use ($username, $password) {
            $api = new MelipayamakApi($username, $password);
            $sms = $api->sms();
            $result = json_decode($sms->getCredit(), true);

            return $result['Value'] ?? null;
        });

        if ($credit !== null) {
            return formatMoney($credit);
        }

        return 'Error: 0';

    }
}

if (! function_exists('getMeliPayamakSendAll')) {
    function getMeliPayamakSendAll()
    {
        $username = env('MELI_PAYAMAK_USERNAME');
        $password = env('MELI_PAYAMAK_PASSWORD');
        $cacheKey = 'getMeliPayamakSendAll';

        $smsTotal = Cache::remember($cacheKey, now()->addMinutes(15), function () use ($username, $password) {
            $api = new MelipayamakApi($username, $password);
            $branch = $api->branch();

            $response = $branch->getTotalSent();

            return $response;
        });

        if ($smsTotal !== null) {
            return formatMoney($smsTotal);
        }

        return 'Error: 0';

    }
}

if (! function_exists('getMeliPayamakBasePrice')) {
    function getMeliPayamakBasePrice()
    {
        $username = env('MELI_PAYAMAK_USERNAME');
        $password = env('MELI_PAYAMAK_PASSWORD');
        $cacheKey = 'getMeliPayamakBasePrices';

        $smsTotal = Cache::remember($cacheKey, now()->addMinutes(15), function () use ($username, $password) {
            $api = new MelipayamakApi($username, $password);
            $sms = $api->sms();

            $response = json_decode($sms->getBasePrice(), true);

            return $response['Value'] ?? null;
        });

        if ($smsTotal !== null) {
            return formatMoney($smsTotal);
        }

        return 'Error: 0';

    }
}

if (! function_exists('getTransactionStatus')) {
    function getTransactionStatus(?string $status = null): array
    {
        if ($status === null) {
            return [
                'text' => 'وضعیت نامشخص',
                'class' => 'bg-gray-100 text-gray-700 rounded-xl px-3 py-0.5',
            ];
        }

        return match ($status) {
            'PAID' => [
                'text' => 'تراکنش‌های موفق عدم اعتبار سنجی',
                'class' => 'bg-blue-100 text-blue-600 rounded-xl px-3 py-0.5',
            ],
            'VERIFIED' => [
                'text' => 'تراکنش‌های موفق اعتبار سنجی شده',
                'class' => 'bg-green-100 text-green-600 rounded-xl px-3 py-0.5',
            ],
            'TRASH' => [
                'text' => 'تراکنش‌های ناموفق',
                'class' => 'bg-red-100 text-red-600 rounded-xl px-3 py-0.5',
            ],
            'ACTIVE' => [
                'text' => 'همه تراکنش‌های موفق',
                'class' => 'bg-yellow-100 text-yellow-600 rounded-xl px-3 py-0.5',
            ],
            'REFUNDED' => [
                'text' => 'تراکنش‌های استرداد شده',
                'class' => 'bg-orange-100 text-orange-600 rounded-xl px-3 py-0.5',
            ],
            default => [
                'text' => $status,
                'class' => 'bg-gray-100 text-gray-700 rounded-xl px-3 py-0.5',
            ],
        };
    }
}

function getQabzinoTransactionStatus($status = null): array
{
    if ($status === null) {
        return [
            'text' => 'وضعیت نامشخص',
            'class' => 'bg-gray-100 text-gray-700 rounded-xl px-3 py-0.5',
        ];
    }

    // تبدیل رشته به بولی در صورت نیاز
    if (is_string($status)) {
        $status = strtolower($status) === 'true';
    }

    return match ($status) {
        true => [
            'text' => 'پرداخت موفق',
            'class' => 'bg-green-500 text-white rounded-xl px-3 py-0.5',
        ],
        false => [
            'text' => 'ناموفق - عدم پرداخت',
            'class' => 'bg-red-100 text-red-600 rounded-xl px-3 py-0.5',
        ],
        default => [
            'text' => $status,
            'class' => 'bg-gray-100 text-gray-700 rounded-xl px-3 py-0.5',
        ],
    };
}

if (! function_exists('getTerminalWithId')) {
    function getTerminalWithId($terminalId)
    {
        $gate = Gate::where('terminalId', $terminalId)->first();
        if (isset($gate)) {
            return $gate->toArray();
        }

        return [];
    }
}

// if (! function_exists('refoundZarinpalWithRefId')) {
//     function refoundZarinpalWithRefId($refId, $amount, $description = 'استرداد وجه', $method = 'PAYA')
//     {
//         $amount = str_replace(',', '', $amount);

//         // return (float) $amount * 10;
//         $response = Http::withHeaders([
//             'Authorization' => 'Bearer '.env('ZARINPAL_ACCESS_TOKEN_KHODROX'),
//             'Content-Type' => 'application/json',
//             'Accept' => 'application/json',
//         ])->post('https://next.zarinpal.com/api/v4/graphql', [
//             'query' => '
//             mutation AddRefund($session_id: ID!, $amount: BigInteger!, $description: String, $method: InstantPayoutActionTypeEnum, $reason: RefundReasonEnum) {
//                 resource: AddRefund(
//                     session_id: $session_id
//                     amount: $amount
//                     description: $description
//                     method: $method
//                     reason: $reason
//                 ) {
//                     terminal_id
//                     id
//                     amount
//                     timeline {
//                         refund_amount
//                         refund_time
//                         refund_status
//                     }
//                 }
//             }
//         ',
//             'variables' => [
//                 'session_id' => $refId,
//                 'amount' => (float) $amount * 10,
//                 'description' => $description,
//                 'method' => $method,
//                 'reason' => 'CUSTOMER_REQUEST',
//             ],
//         ]);

//         if ($response->successful()) {
//             return $response->json();
//         } else {
//             $responseData = $response->json();

//             if (isset($responseData['errors']) && count($responseData['errors']) > 0) {
//                 $errorMessage = $responseData['errors'][0]['fa_message'] ?? 'خطای نامشخص رخ داده است.';

//                 return response()->json(['error' => $errorMessage], $response->status());
//             }

//             return response()->json(['error' => 'خطای نامشخص رخ داده است.'], $response->status());
//         }
//     }
// }

if (! function_exists('refoundZarinpalWithRefId')) {
    function refoundZarinpalWithRefId($refId, $amount, $description = 'استرداد وجه', $method = 'PAYA')
    {
        $amount = str_replace(',', '', $amount);
        $amount = (float) $amount * 10;

        $tokens = [
            env('ZARINPAL_ACCESS_TOKEN_KHODROX'),
            env('ZARINPAL_ACCESS_TOKEN'),
        ];

        foreach ($tokens as $token) {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$token,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post('https://next.zarinpal.com/api/v4/graphql', [
                'query' => '
                    mutation AddRefund($session_id: ID!, $amount: BigInteger!, $description: String, $method: InstantPayoutActionTypeEnum, $reason: RefundReasonEnum) {
                        resource: AddRefund(
                            session_id: $session_id
                            amount: $amount
                            description: $description
                            method: $method
                            reason: $reason
                        ) {
                            terminal_id
                            id
                            amount
                            timeline {
                                refund_amount
                                refund_time
                                refund_status
                            }
                        }
                    }
                ',
                'variables' => [
                    'session_id' => $refId,
                    'amount' => $amount,
                    'description' => $description,
                    'method' => $method,
                    'reason' => 'CUSTOMER_REQUEST',
                ],
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            $responseData = $response->json();
            if (isset($responseData['errors']) && count($responseData['errors']) > 0) {
                $errorMessage = $responseData['errors'][0]['fa_message'] ?? null;

                if ($errorMessage) {
                    // اگر خطای مشخصی دریافت شد، بازگرداندن خطا
                    return response()->json(['error' => $errorMessage], $response->status());
                }

                // اگر خطای نامشخص بود، ادامه با توکن بعدی
                continue;
            }
        }

        // اگر همه توکن‌ها شکست خوردند
        return response()->json(['error' => 'خطای نامشخص رخ داده است.'], 500);
    }
}

function makeSlug($string)
{
    $string = preg_replace('/[^آ-یa-zA-Z0-9\s\-]+/u', '', $string); // نگه‌داشتن حروف فارسی و انگلیسی
    $string = preg_replace('/[\s\-]+/', '-', $string); // تبدیل فاصله به -

    return trim($string, '-'); // حذف - اضافه
}

if (! function_exists('saveFileFromTemporaryUrl')) {
    function saveFileFromTemporaryUrl($temporaryUrl, $directory = 'Receipt')
    {
        try {
            // دریافت محتویات فایل
            $fileContents = file_get_contents($temporaryUrl);

            if (! $fileContents) {
                throw new Exception("Failed to retrieve file contents from URL: $temporaryUrl");
            }

            // تولید نام منحصربه‌فرد برای فایل
            $fileName = uniqid().'.'.pathinfo(parse_url($temporaryUrl, PHP_URL_PATH), PATHINFO_EXTENSION);

            // مسیر ذخیره فایل
            $storagePath = storage_path("app/public/{$directory}/");

            // اطمینان از وجود پوشه
            if (! is_dir($storagePath)) {
                mkdir($storagePath, 0755, true);
            }

            // ذخیره فایل
            file_put_contents($storagePath.$fileName, $fileContents);

            // بازگرداندن آدرس فایل ذخیره‌شده
            return "storage/{$directory}/{$fileName}";
        } catch (Exception $e) {
            // مدیریت خطا
            return null;
        }
    }
}

if (! function_exists('ShebacartInquiryType')) {
    function ShebacartInquiryType(?string $service = 'default'): array
    {
        if ($service === null) {
            return [

                'text' => 'نامشخص',
                'class' => 'bg-gray-100 text-gray-700 rounded-xl px-3 py-0.5',

            ];
        }

        return match ($service) {
            'iban-inquiry-a' => [
                'text' => 'استعلام شبا',
                'class' => 'bg-yellow-300 text-yellow-700 rounded-xl px-3 py-0.5',
            ],
            'card-inquiry-a' => [
                'text' => 'استعلام کارت',
                'class' => 'bg-blue-500 text-white rounded-xl px-3 py-0.5',
            ],
            'deposit-to-iban-a' => [
                'text' => 'حساب به شبا',
                'class' => 'bg-green-500 text-white rounded-xl px-3 py-0.5',
            ],
            'card-to-deposit-a' => [
                'text' => 'کارت به حساب',
                'class' => 'bg-red-500 text-white rounded-xl px-3 py-0.5',
            ],
            'card-to-iban-a' => [
                'text' => 'کارت به شبا',
                'class' => 'bg-purple-500 text-white rounded-xl px-3 py-0.5',
            ],
            'sayad-inquiry-b' => [
                'text' => 'چک صیادی',
                'class' => 'bg-pink-500 text-white rounded-xl px-3 py-0.5',

            ],

            default => [
                'text' => 'نامشخص',
                'class' => 'bg-gray-100 text-gray-700 rounded-xl px-3 py-0.5',
            ],
        };
    }
}

function formatCardNumber(?string $cardNumber): string
{
    if (! $cardNumber) {
        return '';
    }

    return trim(chunk_split(preg_replace('/\D/', '', $cardNumber), 4, '-'), '-');
}

function curl_get_json($url, $timeoutSeconds = 5)
{

    $ch = curl_init($url);

    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => $timeoutSeconds,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
        ],
    ]);

    $response = curl_exec($ch);
    $error = curl_error($ch);
    curl_close($ch);

    if ($response === false) {
        return ['error' => $error];
    }

    $decoded = json_decode($response, true);

    return $decoded ?: ['error' => 'Invalid JSON'];
}

function getCountryInfo($code)
{
    $response = file_get_contents('https://restcountries.com/v3.1/alpha/'.$code);
    if ($response === false) {
        return null;
    }

    $data = json_decode($response, true);
    if (isset($data[0])) {
        return $data[0];
    }

    return $data; // بعضی موارد مستقیم بدون [0] برمی‌گرده
}

function getSourceBalance(string $phone, string $source): float
{
    $balance = User::where('phone', $phone)
        ->where('source', $source)
        ->value('balance');

    $balance = str_replace([',', ' '], '', $balance);

    return (float) $balance ?? 0.0;
}

function getTransferAppSetting()
{
    return Setting::where('source', 'khodrox')->where('key', 'transfer_khdrox_khalafiyar')->where('value', 'ACTIVE')->first();
}

function sendQabzinoBot(string $message): void
{
    $settings = Setting::whereIn('key', ['botKey', 'usernameChanel'])->pluck('value', 'key');

    $botKey = $settings['botKey'] ?? null;
    $chatId = $settings['usernameChanel'] ?? null;

    if (! $botKey || ! $chatId) {
        Log::warning('Qabzino botKey or usernameChanel is missing.');

        return;
    }

    Http::withHeaders([
        'Content-Type' => 'application/json',
    ])
        ->post("https://tapi.bale.ai/bot{$botKey}/sendMessage", [
            'chat_id' => $chatId,
            'text' => $message,
        ]);
}
