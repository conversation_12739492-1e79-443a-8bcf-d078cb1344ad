<?php

namespace App\Jobs;

use App\Models\Article;
use Carbon\Carbon;
use Hekmatinasser\Verta\Verta;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ArticlePublishedJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $articles = Article::where('status', 'draft')
            ->where('active', true)
            ->get()
            ->filter(function ($article) {
                $jalali = Verta::parse($article->datePublished);
                $gregorian = $jalali->datetime();

                return Carbon::now()->greaterThanOrEqualTo($gregorian);
            });

        foreach ($articles as $article) {

            $article->status = 'published';

            $article->save();
        }

    }
}
