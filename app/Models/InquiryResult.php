<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model;

class InquiryResult extends Model
{
    protected $collection = 'inquiry_results';

    protected $fillable = [
        'user_id',
        'balance',
        'balance_after',
        'source',
        'trace_number',
        'phone',
        'type',
        'detail_phone',
        'detail_national_id',
        'plaque_left',
        'plaque_mid',
        'plaque_right',
        'plaque_alphabet',
        'data',
        'result',
        'status',
        'agent',
        'redirect',
        'mobile_number',
        'national_id',
        'license_number',
    ];

    public function scopeWhereToday($query, $field1 = 'created_at', $field2 = 'created_time')
    {
        $today = \Carbon\Carbon::today();

        return $query->where(function ($q) use ($today, $field1, $field2) {
            $q->whereDate($field1, $today)
                ->orWhereDate($field2, $today);
        });
    }
}
