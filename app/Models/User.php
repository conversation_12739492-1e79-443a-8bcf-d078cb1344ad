<?php

namespace App\Models;

use App\Traits\Models\User\Functions;
use App\Traits\Models\User\Relations as UserRelations;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;
use Laravel\Passport\HasApiTokens;
use MongoDB\Laravel\Auth\User as Authenticatable;

class User extends Authenticatable
{
    use Functions, HasApiTokens, HasFactory, Notifiable, UserRelations;

    protected $connection = 'mongodb';

    protected $collection = 'users';

    protected $fillable = [
        '_id',
        'fullname',
        'phone',
        'balance',
        'level',
        'withdrawalPendingBalance',
        'source',
        'depositVisit',
        'gateVisit',
        'selectedPrices',
        'access_token',
    ];

    protected $primaryKey = '_id';

    protected $casts = [
        '_id' => 'string',
        'phone' => 'string',
        'balance' => 'string',
    ];

    protected function balance(): Attribute
    {
        return Attribute::make(
            get: fn (?string $value) => $value !== null ? formatMoney($value) : null,
        );
    }

    public function roles()
    {
        return $this->belongsToMany(Role::class);
    }

    public function hasRole($role)
    {
        if (is_string($role)) {
            return $this->roles->contains('name', $role);
        }

        return (bool) $role->intersect($this->roles)->count();
    }

    public function scopeWhereToday($query, $field1 = 'created_at', $field2 = 'created_time')
    {
        $today = \Carbon\Carbon::today();

        return $query->where(function ($q) use ($today, $field1, $field2) {
            $q->whereDate($field1, $today)
                ->orWhereDate($field2, $today);
        });
    }
}
