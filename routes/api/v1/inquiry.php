<?php

use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Api\\v1', 'prefix' => 'v1'], function () {

    Route::group(['prefix' => '/user'], function () {

        Route::get('/history', 'InquiryController@view');

        Route::post('/inquiry/picture', 'InquiryPictureController@show');

        Route::get('/inquiry/result/{tranceNumber}', 'InquiryController@show');

        Route::post('/inquiry/{type}', 'InquiryController@index');

        Route::prefix('inquiry')->group(function () {
            Route::post('certificate/negative-points', 'CertificateController@store');
            Route::get('certificate/{traceNumber}/negative-points', 'CertificateController@show');
            // Route::post('certificate/status', 'CertificateController@status');
            // Route::post('vehicle/insurance', 'VehicleController@insurance');
            // Route::post('vehicle/document', 'VehicleController@document');
            // Route::post('plate/history', 'PlateController@history');
            // Route::post('toll/highway', 'TollController@highway');
        });



    });

});
